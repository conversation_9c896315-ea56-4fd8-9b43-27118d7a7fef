<!DOCTYPE html>
<html lang="cs">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <meta name="theme-color" content="#1f2937" />
    <meta
      name="description"
      content="<PERSON> - Vývoj webových a desktopových aplikací, IT bezpečnost, správa VPS serverů. Profesionální IT služby pro firmy i jednotlivce."
    />
    <meta name="keywords" content="vývoj aplikací, IT bezpečnost, VPS servery, webové aplikace, desktopové aplikace, <PERSON>, IT služby" />
    <meta name="author" content="<PERSON>" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:title" content="<PERSON> - Vývoj & Bezpečnost | IT & Servery" />
    <meta property="og:description" content="Profesionální vývoj aplikací, IT bezpečnost a správa VPS serverů. Spolehlivé IT služby pro moderní firmy." />
    <meta property="og:image" content="%PUBLIC_URL%/logo1.png" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:title" content="Martin Vašíček - Vývoj & Bezpečnost | IT & Servery" />
    <meta property="twitter:description" content="Profesionální vývoj aplikací, IT bezpečnost a správa VPS serverů. Spolehlivé IT služby pro moderní firmy." />

    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo1.png" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />

    <!-- Preconnect to external domains for better performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <title>Martin Vašíček - Vývoj & Bezpečnost | IT & Servery</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>
