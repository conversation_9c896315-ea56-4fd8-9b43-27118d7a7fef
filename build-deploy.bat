@echo off
setlocal enabledelayedexpansion

:: Simple batch script to build and deploy the personal website
:: This script builds the React app and copies it to the html/ folder

echo.
echo 🚀 Starting deployment build process...
echo.

:: Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

:: Check if npm is available
npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ npm is not available
    pause
    exit /b 1
)

:: Clean the html directory
echo [CLEAN] Cleaning target directory...
if exist "html" (
    rmdir /s /q "html"
    echo ✅ Removed existing html directory
)
mkdir "html"
echo ✅ Created fresh html directory

:: Check if node_modules exists, install if not
if not exist "node_modules" (
    echo [INSTALL] Installing dependencies...
    npm install
    if errorlevel 1 (
        echo ❌ Failed to install dependencies
        pause
        exit /b 1
    )
    echo ✅ Dependencies installed successfully
)

:: Build the React application
echo [BUILD] Building React application...
npm run build
if errorlevel 1 (
    echo ❌ Failed to build React application
    pause
    exit /b 1
)
echo ✅ React application built successfully

:: Copy build files to html directory
echo [COPY] Copying build files to html directory...
if not exist "build" (
    echo ❌ Build directory does not exist
    pause
    exit /b 1
)

xcopy "build\*" "html\" /E /I /Y >nul
if errorlevel 1 (
    echo ❌ Failed to copy build files
    pause
    exit /b 1
)
echo ✅ Copied all files from build to html

:: Validate the build
echo [VALIDATE] Validating build output...
if not exist "html\index.html" (
    echo ❌ index.html not found in build output
    pause
    exit /b 1
)
echo ✅ Found required file: index.html

if not exist "html\static" (
    echo ❌ static directory not found in build output
    pause
    exit /b 1
)
echo ✅ Found required directory: static

:: Generate build info
echo [INFO] Generating build information...
echo { > "html\build-info.json"
echo   "buildDate": "%date% %time%", >> "html\build-info.json"
echo   "platform": "Windows", >> "html\build-info.json"
echo   "buildTool": "batch script" >> "html\build-info.json"
echo } >> "html\build-info.json"
echo ✅ Generated build-info.json

echo.
echo 🎉 Build completed successfully!
echo 📁 Deployable files are ready in: html/
echo.
echo Next steps:
echo   1. Test the build: cd html ^&^& python -m http.server 8000
echo   2. Deploy the html/ folder to your web server
echo.
pause
