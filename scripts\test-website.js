#!/usr/bin/env node

const fs = require('fs-extra');
const path = require('path');

// Test configuration
const config = {
  htmlDir: 'html',
  requiredFiles: [
    'index.html',
    'manifest.json',
    'robots.txt',
    'favicon.ico',
    'build-info.json'
  ],
  requiredDirs: [
    'static',
    'static/css',
    'static/js'
  ],
  logoFiles: [
    'logo1.png',
    'logo2.png',
    'logo3.png',
    'logo4.png'
  ]
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function logTest(test, message) {
  log(`${colors.cyan}[${test}]${colors.reset} ${message}`);
}

function logPass(message) {
  log(`${colors.green}✓ ${message}${colors.reset}`);
}

function logFail(message) {
  log(`${colors.red}✗ ${message}${colors.reset}`);
}

function logWarning(message) {
  log(`${colors.yellow}⚠ ${message}${colors.reset}`);
}

async function testDirectoryExists() {
  logTest('DIRECTORY', 'Checking if html directory exists...');
  
  if (!await fs.pathExists(config.htmlDir)) {
    logFail(`${config.htmlDir} directory does not exist`);
    return false;
  }
  
  logPass(`${config.htmlDir} directory exists`);
  return true;
}

async function testRequiredFiles() {
  logTest('FILES', 'Checking required files...');
  let allFilesExist = true;
  
  for (const file of config.requiredFiles) {
    const filePath = path.join(config.htmlDir, file);
    if (!await fs.pathExists(filePath)) {
      logFail(`Required file missing: ${file}`);
      allFilesExist = false;
    } else {
      const stats = await fs.stat(filePath);
      if (stats.size === 0) {
        logFail(`Required file is empty: ${file}`);
        allFilesExist = false;
      } else {
        logPass(`Found ${file} (${(stats.size / 1024).toFixed(2)} KB)`);
      }
    }
  }
  
  return allFilesExist;
}

async function testRequiredDirectories() {
  logTest('DIRECTORIES', 'Checking required directories...');
  let allDirsExist = true;
  
  for (const dir of config.requiredDirs) {
    const dirPath = path.join(config.htmlDir, dir);
    if (!await fs.pathExists(dirPath)) {
      logFail(`Required directory missing: ${dir}`);
      allDirsExist = false;
    } else {
      logPass(`Found directory: ${dir}`);
    }
  }
  
  return allDirsExist;
}

async function testLogoFiles() {
  logTest('LOGOS', 'Checking logo files...');
  let allLogosExist = true;
  
  for (const logo of config.logoFiles) {
    const logoPath = path.join(config.htmlDir, logo);
    if (!await fs.pathExists(logoPath)) {
      logWarning(`Logo file missing: ${logo}`);
      allLogosExist = false;
    } else {
      const stats = await fs.stat(logoPath);
      logPass(`Found ${logo} (${(stats.size / 1024).toFixed(2)} KB)`);
    }
  }
  
  return allLogosExist;
}

async function testIndexHtml() {
  logTest('HTML', 'Analyzing index.html...');
  
  const indexPath = path.join(config.htmlDir, 'index.html');
  if (!await fs.pathExists(indexPath)) {
    logFail('index.html does not exist');
    return false;
  }
  
  const content = await fs.readFile(indexPath, 'utf8');
  
  // Test for essential HTML elements
  const tests = [
    { name: 'DOCTYPE declaration', regex: /<!DOCTYPE html>/i },
    { name: 'HTML lang attribute', regex: /<html[^>]+lang=/i },
    { name: 'Viewport meta tag', regex: /<meta[^>]+name="viewport"/i },
    { name: 'Title tag', regex: /<title>.*Martin.*<\/title>/i },
    { name: 'Meta description', regex: /<meta[^>]+name="description"/i },
    { name: 'Root div', regex: /<div[^>]+id="root"/i },
    { name: 'React bundle', regex: /static\/js\/main\.[a-f0-9]+\.js/i },
    { name: 'CSS bundle', regex: /static\/css\/main\.[a-f0-9]+\.css/i }
  ];
  
  let allTestsPassed = true;
  
  for (const test of tests) {
    if (test.regex.test(content)) {
      logPass(`HTML contains ${test.name}`);
    } else {
      logFail(`HTML missing ${test.name}`);
      allTestsPassed = false;
    }
  }
  
  return allTestsPassed;
}

async function testStaticAssets() {
  logTest('ASSETS', 'Checking static assets...');
  
  const staticDir = path.join(config.htmlDir, 'static');
  if (!await fs.pathExists(staticDir)) {
    logFail('Static directory does not exist');
    return false;
  }
  
  // Check CSS files
  const cssDir = path.join(staticDir, 'css');
  if (await fs.pathExists(cssDir)) {
    const cssFiles = await fs.readdir(cssDir);
    const mainCss = cssFiles.find(file => file.startsWith('main.') && file.endsWith('.css'));
    if (mainCss) {
      const cssPath = path.join(cssDir, mainCss);
      const stats = await fs.stat(cssPath);
      logPass(`Found CSS bundle: ${mainCss} (${(stats.size / 1024).toFixed(2)} KB)`);
    } else {
      logFail('Main CSS bundle not found');
      return false;
    }
  }
  
  // Check JS files
  const jsDir = path.join(staticDir, 'js');
  if (await fs.pathExists(jsDir)) {
    const jsFiles = await fs.readdir(jsDir);
    const mainJs = jsFiles.find(file => file.startsWith('main.') && file.endsWith('.js'));
    if (mainJs) {
      const jsPath = path.join(jsDir, mainJs);
      const stats = await fs.stat(jsPath);
      logPass(`Found JS bundle: ${mainJs} (${(stats.size / 1024).toFixed(2)} KB)`);
    } else {
      logFail('Main JS bundle not found');
      return false;
    }
  }
  
  return true;
}

async function testBuildInfo() {
  logTest('BUILD-INFO', 'Checking build information...');
  
  const buildInfoPath = path.join(config.htmlDir, 'build-info.json');
  if (!await fs.pathExists(buildInfoPath)) {
    logWarning('build-info.json does not exist');
    return false;
  }
  
  try {
    const buildInfo = await fs.readJson(buildInfoPath);
    
    const requiredFields = ['buildDate', 'buildVersion', 'nodeVersion', 'platform'];
    let allFieldsPresent = true;
    
    for (const field of requiredFields) {
      if (buildInfo[field]) {
        logPass(`Build info contains ${field}: ${buildInfo[field]}`);
      } else {
        logFail(`Build info missing ${field}`);
        allFieldsPresent = false;
      }
    }
    
    return allFieldsPresent;
  } catch (error) {
    logFail(`Invalid build-info.json: ${error.message}`);
    return false;
  }
}

async function generateTestReport(results) {
  logTest('REPORT', 'Generating test report...');
  
  const report = {
    timestamp: new Date().toISOString(),
    totalTests: Object.keys(results).length,
    passedTests: Object.values(results).filter(Boolean).length,
    failedTests: Object.values(results).filter(result => !result).length,
    results: results,
    recommendations: []
  };
  
  // Add recommendations based on results
  if (!results.logoFiles) {
    report.recommendations.push('Consider adding missing logo files for better visual presentation');
  }
  
  if (!results.buildInfo) {
    report.recommendations.push('Build info file helps with debugging deployment issues');
  }
  
  const reportPath = path.join(config.htmlDir, 'test-report.json');
  await fs.writeJson(reportPath, report, { spaces: 2 });
  
  logPass(`Test report saved to ${reportPath}`);
  return report;
}

async function main() {
  log(`${colors.bright}${colors.cyan}🧪 Testing website build...${colors.reset}\n`);
  
  const results = {};
  
  try {
    results.directory = await testDirectoryExists();
    if (!results.directory) {
      log(`\n${colors.red}Cannot continue testing without html directory${colors.reset}`);
      process.exit(1);
    }
    
    results.requiredFiles = await testRequiredFiles();
    results.requiredDirectories = await testRequiredDirectories();
    results.logoFiles = await testLogoFiles();
    results.indexHtml = await testIndexHtml();
    results.staticAssets = await testStaticAssets();
    results.buildInfo = await testBuildInfo();
    
    const report = await generateTestReport(results);
    
    log(`\n${colors.bright}📊 Test Results:${colors.reset}`);
    log(`${colors.green}✓ Passed: ${report.passedTests}/${report.totalTests}${colors.reset}`);
    log(`${colors.red}✗ Failed: ${report.failedTests}/${report.totalTests}${colors.reset}`);
    
    if (report.recommendations.length > 0) {
      log(`\n${colors.yellow}💡 Recommendations:${colors.reset}`);
      report.recommendations.forEach(rec => log(`  • ${rec}`));
    }
    
    if (report.failedTests === 0) {
      log(`\n${colors.bright}${colors.green}🎉 All critical tests passed! Website is ready for deployment.${colors.reset}`);
    } else {
      log(`\n${colors.bright}${colors.yellow}⚠️  Some tests failed. Please review and fix issues before deployment.${colors.reset}`);
    }
    
  } catch (error) {
    log(`\n${colors.bright}${colors.red}💥 Testing failed!${colors.reset}`);
    logFail(error.message);
    process.exit(1);
  }
}

// Run tests
if (require.main === module) {
  main();
}

module.exports = { main, config };
