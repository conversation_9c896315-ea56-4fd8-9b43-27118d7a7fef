# PowerShell Build and Deploy Script for Personal Website
# This script builds the React app and copies it to the html/ folder for deployment

param(
    [switch]$Clean = $false,
    [switch]$SkipBuild = $false,
    [switch]$Verbose = $false
)

# Configuration
$SourceDir = "build"
$TargetDir = "html"
$PublicDir = "public"

# Colors for output
$Colors = @{
    Red = "Red"
    Green = "Green"
    Yellow = "Yellow"
    Blue = "Blue"
    Cyan = "Cyan"
    Magenta = "Magenta"
}

function Write-Step {
    param([string]$Step, [string]$Message)
    Write-Host "[$Step] " -ForegroundColor Cyan -NoNewline
    Write-Host $Message
}

function Write-Success {
    param([string]$Message)
    Write-Host "✓ " -ForegroundColor Green -NoNewline
    Write-Host $Message
}

function Write-Error {
    param([string]$Message)
    Write-Host "✗ " -ForegroundColor Red -NoNewline
    Write-Host $Message
}

function Write-Warning {
    param([string]$Message)
    Write-Host "⚠ " -ForegroundColor Yellow -NoNewline
    Write-Host $Message
}

function Clean-TargetDirectory {
    Write-Step "CLEAN" "Cleaning target directory..."
    
    if (Test-Path $TargetDir) {
        try {
            Remove-Item -Path $TargetDir -Recurse -Force
            Write-Success "Removed existing $TargetDir directory"
        }
        catch {
            Write-Error "Failed to remove $TargetDir directory: $($_.Exception.Message)"
            exit 1
        }
    }
    
    try {
        New-Item -ItemType Directory -Path $TargetDir -Force | Out-Null
        Write-Success "Created fresh $TargetDir directory"
    }
    catch {
        Write-Error "Failed to create $TargetDir directory: $($_.Exception.Message)"
        exit 1
    }
}

function Build-ReactApp {
    Write-Step "BUILD" "Building React application..."
    
    # Check if node_modules exists
    if (-not (Test-Path "node_modules")) {
        Write-Warning "node_modules not found. Installing dependencies..."
        try {
            npm install
            if ($LASTEXITCODE -ne 0) {
                throw "npm install failed"
            }
        }
        catch {
            Write-Error "Failed to install dependencies: $($_.Exception.Message)"
            exit 1
        }
    }
    
    # Build the React app
    try {
        npm run build
        if ($LASTEXITCODE -ne 0) {
            throw "npm run build failed"
        }
        Write-Success "React application built successfully"
    }
    catch {
        Write-Error "Failed to build React application: $($_.Exception.Message)"
        exit 1
    }
}

function Copy-BuildFiles {
    Write-Step "COPY" "Copying build files to target directory..."
    
    if (-not (Test-Path $SourceDir)) {
        Write-Error "Build directory $SourceDir does not exist. Run build first."
        exit 1
    }
    
    try {
        Copy-Item -Path "$SourceDir\*" -Destination $TargetDir -Recurse -Force
        Write-Success "Copied all files from $SourceDir to $TargetDir"
    }
    catch {
        Write-Error "Failed to copy build files: $($_.Exception.Message)"
        exit 1
    }
}

function Copy-AdditionalAssets {
    Write-Step "ASSETS" "Copying additional assets..."
    
    $AdditionalAssets = @("robots.txt", "manifest.json", "favicon.ico")
    
    foreach ($Asset in $AdditionalAssets) {
        $SourcePath = Join-Path $PublicDir $Asset
        $TargetPath = Join-Path $TargetDir $Asset
        
        if (Test-Path $SourcePath) {
            try {
                Copy-Item -Path $SourcePath -Destination $TargetPath -Force
                Write-Success "Copied $Asset"
            }
            catch {
                Write-Warning "Could not copy $Asset : $($_.Exception.Message)"
            }
        }
    }
}

function Test-BuildOutput {
    Write-Step "VALIDATE" "Validating build output..."
    
    $RequiredFiles = @("index.html")
    $RequiredDirs = @("static")
    
    foreach ($File in $RequiredFiles) {
        $FilePath = Join-Path $TargetDir $File
        if (-not (Test-Path $FilePath)) {
            Write-Error "Required file $File not found in build output"
            exit 1
        }
        Write-Success "Found required file: $File"
    }
    
    foreach ($Dir in $RequiredDirs) {
        $DirPath = Join-Path $TargetDir $Dir
        if (-not (Test-Path $DirPath)) {
            Write-Error "Required directory $Dir not found in build output"
            exit 1
        }
        Write-Success "Found required directory: $Dir"
    }
    
    # Check file sizes
    $IndexPath = Join-Path $TargetDir "index.html"
    $FileInfo = Get-Item $IndexPath
    if ($FileInfo.Length -eq 0) {
        Write-Error "index.html is empty"
        exit 1
    }
    $SizeKB = [math]::Round($FileInfo.Length / 1024, 2)
    Write-Success "index.html size: $SizeKB KB"
}

function New-BuildInfo {
    Write-Step "INFO" "Generating build information..."
    
    try {
        $PackageJson = Get-Content "package.json" | ConvertFrom-Json
        $BuildInfo = @{
            buildDate = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
            buildVersion = $PackageJson.version
            nodeVersion = (node --version)
            platform = $env:OS
            powershellVersion = $PSVersionTable.PSVersion.ToString()
        }
        
        $BuildInfoPath = Join-Path $TargetDir "build-info.json"
        $BuildInfo | ConvertTo-Json -Depth 2 | Set-Content $BuildInfoPath
        Write-Success "Generated build-info.json"
    }
    catch {
        Write-Warning "Could not generate build info: $($_.Exception.Message)"
    }
}

# Main execution
$StartTime = Get-Date

Write-Host "🚀 Starting deployment build process..." -ForegroundColor Magenta
Write-Host ""

try {
    if ($Clean -or -not $SkipBuild) {
        Clean-TargetDirectory
    }
    
    if (-not $SkipBuild) {
        Build-ReactApp
    }
    
    Copy-BuildFiles
    Copy-AdditionalAssets
    Test-BuildOutput
    New-BuildInfo
    
    $Duration = [math]::Round(((Get-Date) - $StartTime).TotalSeconds, 2)
    
    Write-Host ""
    Write-Host "🎉 Build completed successfully!" -ForegroundColor Green
    Write-Host "📁 Deployable files are ready in: $TargetDir/" -ForegroundColor Green
    Write-Host "⏱️  Build time: ${Duration}s" -ForegroundColor Green
    Write-Host ""
    
    Write-Host "Next steps:" -ForegroundColor Cyan
    Write-Host "  1. Test the build: " -NoNewline
    Write-Host "cd $TargetDir && python -m http.server 8000" -ForegroundColor Yellow
    Write-Host "  2. Deploy the " -NoNewline
    Write-Host "$TargetDir/" -ForegroundColor Yellow -NoNewline
    Write-Host " folder to your web server"
}
catch {
    Write-Host ""
    Write-Host "💥 Build failed!" -ForegroundColor Red
    Write-Error $_.Exception.Message
    exit 1
}
