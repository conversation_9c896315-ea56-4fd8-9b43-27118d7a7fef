{"version": 3, "file": "static/css/main.1499c72e.css", "mappings": "AAAA,mDAAc,CAAd,uBAAc,CAAd,kBAAc,CAAd,kBAAc,CAAd,aAAc,CAAd,aAAc,CAAd,aAAc,CAAd,cAAc,CAAd,cAAc,CAAd,YAAc,CAAd,YAAc,CAAd,iBAAc,CAAd,qCAAc,CAAd,6BAAc,CAAd,4BAAc,CAAd,2BAAc,CAAd,cAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,iBAAc,CAAd,0BAAc,CAAd,2BAAc,CAAd,yBAAc,CAAd,iCAAc,CAAd,0BAAc,CAAd,qBAAc,CAAd,6BAAc,CAAd,WAAc,CAAd,iBAAc,CAAd,eAAc,CAAd,gBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,eAAc,CAAd,YAAc,CAAd,kBAAc,CAAd,oBAAc,CAAd,0BAAc,CAAd,wBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,oBAAc,CAAd,oBAAc,CAAd;;CAAc,CAAd,uCAAc,CAAd,qBAAc,CAAd,8BAAc,CAAd,wCAAc,CAAd,4BAAc,CAAd,uCAAc,CAAd,gHAAc,CAAd,8BAAc,CAAd,eAAc,CAAd,UAAc,CAAd,wBAAc,CAAd,QAAc,CAAd,uBAAc,CAAd,aAAc,CAAd,QAAc,CAAd,4DAAc,CAAd,gCAAc,CAAd,mCAAc,CAAd,mBAAc,CAAd,eAAc,CAAd,uBAAc,CAAd,2BAAc,CAAd,8CAAc,CAAd,mGAAc,CAAd,aAAc,CAAd,8BAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,aAAc,CAAd,iBAAc,CAAd,sBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,8BAAc,CAAd,oBAAc,CAAd,aAAc,CAAd,mEAAc,CAAd,aAAc,CAAd,mBAAc,CAAd,cAAc,CAAd,+BAAc,CAAd,mBAAc,CAAd,sBAAc,CAAd,mBAAc,CAAd,QAAc,CAAd,SAAc,CAAd,iCAAc,CAAd,gHAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,4BAAc,CAAd,gCAAc,CAAd,+BAAc,CAAd,mEAAc,CAAd,0CAAc,CAAd,mBAAc,CAAd,mDAAc,CAAd,sDAAc,CAAd,YAAc,CAAd,yBAAc,CAAd,2DAAc,CAAd,iBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,QAAc,CAAd,SAAc,CAAd,gBAAc,CAAd,wBAAc,CAAd,sDAAc,CAAd,SAAc,CAAd,mCAAc,CAAd,wBAAc,CAAd,4DAAc,CAAd,qBAAc,CAAd,qBAAc,CAAd,cAAc,CAAd,uDAAc,CAEd,yBAAmB,CAAnB,iBAAmB,CAAnB,8BAAmB,CAAnB,0BAAmB,CAAnB,wBAAmB,CAAnB,0BAAmB,CAAnB,2BAAmB,CAAnB,sBAAmB,CAAnB,qBAAmB,CAAnB,qBAAmB,CAAnB,oBAAmB,CAAnB,kBAAmB,CAAnB,gCAAmB,CAAnB,kBAAmB,CAAnB,iBAAmB,CAAnB,+BAAmB,CAAnB,8BAAmB,CAAnB,kBAAmB,CAAnB,0BAAmB,CAAnB,0BAAmB,CAAnB,8BAAmB,CAAnB,0DAAmB,CAAnB,+BAAmB,CAAnB,gCAAmB,CAAnB,yCAAmB,CAAnB,sCAAmB,CAAnB,iBAAmB,CAAnB,eAAmB,CAAnB,kBAAmB,CAAnB,gEAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,wGAAmB,CAAnB,+BAAmB,CAAnB,iCAAmB,CAAnB,kCAAmB,CAAnB,+BAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,2CAAmB,CAAnB,2CAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,qDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,2CAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,qDAAmB,CAAnB,6FAAmB,CAAnB,qFAAmB,CAAnB,0EAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,0EAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,gEAAmB,CAAnB,sGAAmB,CAAnB,oEAAmB,CAAnB,sEAAmB,CAAnB,0CAAmB,CAAnB,oBAAmB,CAAnB,oBAAmB,CAAnB,mBAAmB,CAAnB,uBAAmB,CAAnB,kBAAmB,CAAnB,0BAAmB,CAAnB,qBAAmB,CAAnB,+CAAmB,CAAnB,4CAAmB,CAAnB,mDAAmB,CAAnB,uBAAmB,CAAnB,8BAAmB,CAAnB,0BAAmB,CAAnB,gBAAmB,CAAnB,2BAAmB,CAAnB,kBAAmB,CAAnB,yBAAmB,CAAnB,kBAAmB,CAAnB,0BAAmB,CAAnB,mBAAmB,CAAnB,0BAAmB,CAAnB,mBAAmB,CAAnB,0BAAmB,CAAnB,+BAAmB,CAAnB,4BAAmB,CAAnB,8BAAmB,CAAnB,+BAAmB,CAAnB,oCAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,6BAAmB,CAAnB,+BAAmB,CAAnB,UAAmB,CAAnB,+CAAmB,CAAnB,kEAAmB,CAAnB,4FAAmB,CAAnB,mEAAmB,CAAnB,kGAAmB,CAAnB,mDAAmB,CAAnB,4DAAmB,CAAnB,0EAAmB,CAAnB,iGAAmB,CAAnB,+CAAmB,CAAnB,kGAAmB,CAAnB,mGAAmB,CAAnB,gLAAmB,CAAnB,2CAAmB,CAAnB,8QAAmB,CAAnB,sQAAmB,CAAnB,0LAAmB,CAAnB,6IAAmB,CAAnB,qKAAmB,CAAnB,kDAAmB,CAAnB,qIAAmB,CAAnB,kDAAmB,CAAnB,4EAAmB,CAAnB,kDAAmB,CAGnB,MACE,gBACF,CAEA,KAAO,sBAAyB,CAChC,KAEE,iBAAkB,CADlB,0GAEF,CAGA,eACE,yBAAgC,CAAhC,+BAAgC,CAChC,kBACF,CAGA,mBACE,iCAAoC,CACpC,kCACF,CAGA,IAAuB,WAAY,CAA7B,cAA+B,CAGrC,oBACE,gDAAmD,cAAiB,CACtE,CAjCA,yCAkCA,CAlCA,iBAkCA,CAlCA,6LAkCA,CAlCA,mDAkCA,CAlCA,oBAkCA,CAlCA,uDAkCA,CAlCA,2CAkCA,CAlCA,wBAkCA,CAlCA,uDAkCA,CAlCA,gEAkCA,CAlCA,4DAkCA,CAlCA,+CAkCA,CAlCA,kGAkCA,CAlCA,kDAkCA,CAlCA,kBAkCA,CAlCA,+HAkCA,CAlCA,wGAkCA,CAlCA,uEAkCA,CAlCA,wFAkCA,CAlCA,+CAkCA,CAlCA,yDAkCA,CAlCA,sDAkCA,CAlCA,4BAkCA,CAlCA,+BAkCA,CAlCA,+BAkCA,CAlCA,0BAkCA,CAlCA,0BAkCA,CAlCA,2BAkCA,CAlCA,uBAkCA,CAlCA,mCAkCA,CAlCA,8BAkCA,CAlCA,8DAkCA,CAlCA,mBAkCA,CAlCA,qBAkCA,CAlCA,oEAkCA,CAlCA,wGAkCA,CAlCA,mEAkCA,CAlCA,8GAkCA,CAlCA,uBAkCA,CAlCA,qBAkCA,CAlCA,+CAkCA,CAlCA,8BAkCA,CAlCA,gBAkCA,CAlCA,gCAkCA,CAlCA,mBAkCA,CAlCA,4BAkCA,CAlCA,aAkCA,CAlCA,6BAkCA,CAlCA,kBAkCA,CAlCA,+BAkCA,CAlCA,mBAkCA,EAlCA,uDAkCA,CAlCA,4BAkCA,CAlCA,4BAkCA,CAlCA,0BAkCA,CAlCA,qBAkCA,CAlCA,8DAkCA,CAlCA,8DAkCA,CAlCA,gCAkCA,CAlCA,mBAkCA,CAlCA,oEAkCA,CAlCA,wGAkCA,CAlCA,mEAkCA,CAlCA,4GAkCA,CAlCA,wBAkCA,CAlCA,sBAkCA,CAlCA,+CAkCA,CAlCA,8BAkCA,CAlCA,gBAkCA,CAlCA,gCAkCA,CAlCA,mBAkCA,CAlCA,+BAkCA,CAlCA,kBAkCA,CAlCA,4BAkCA,CAlCA,aAkCA,CAlCA,8BAkCA,CAlCA,aAkCA,CAlCA,8BAkCA,CAlCA,mBAkCA", "sources": ["index.css"], "sourcesContent": ["@tailwind base;\n@tailwind components;\n@tailwind utilities;\n\n/* Base typography and colors */\n:root {\n  --accent: #60a5fa; /* tailwind sky-400 */\n}\n\nhtml { scroll-behavior: smooth; }\nbody {\n  font-family: 'Inter', system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, \"Apple Color Emoji\", \"Segoe UI Emoji\";\n  color-scheme: dark;\n}\n\n/* Improve focus visibility */\n:focus-visible {\n  outline: 3px solid var(--accent);\n  outline-offset: 2px;\n}\n\n/* Utility tweaks */\n.container-padding {\n  padding-left: clamp(1rem, 4vw, 3rem);\n  padding-right: clamp(1rem, 4vw, 3rem);\n}\n\n/* Responsive helpers for images */\nimg { max-width: 100%; height: auto; }\n\n/* Reduce hover transform on touch devices */\n@media (hover: none) {\n  .hover\\:scale-110:hover, .hover\\:scale-105:hover { transform: none; }\n}\n"], "names": [], "sourceRoot": ""}