#!/usr/bin/env node

const fs = require('fs-extra');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const config = {
  sourceDir: 'build',
  targetDir: 'html',
  publicDir: 'public',
  srcDir: 'src',
  nodeModulesDir: 'node_modules'
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function logStep(step, message) {
  log(`${colors.cyan}[${step}]${colors.reset} ${message}`);
}

function logSuccess(message) {
  log(`${colors.green}✓ ${message}${colors.reset}`);
}

function logError(message) {
  log(`${colors.red}✗ ${message}${colors.reset}`);
}

function logWarning(message) {
  log(`${colors.yellow}⚠ ${message}${colors.reset}`);
}

async function cleanTargetDirectory() {
  logStep('CLEAN', 'Cleaning target directory...');
  try {
    if (await fs.pathExists(config.targetDir)) {
      await fs.remove(config.targetDir);
      logSuccess(`Removed existing ${config.targetDir} directory`);
    }
    await fs.ensureDir(config.targetDir);
    logSuccess(`Created fresh ${config.targetDir} directory`);
  } catch (error) {
    logError(`Failed to clean target directory: ${error.message}`);
    throw error;
  }
}

async function buildReactApp() {
  logStep('BUILD', 'Building React application...');
  try {
    // Check if node_modules exists
    if (!await fs.pathExists(config.nodeModulesDir)) {
      logWarning('node_modules not found. Installing dependencies...');
      execSync('npm install', { stdio: 'inherit' });
    }

    // Build the React app
    execSync('npm run build', { stdio: 'inherit' });
    logSuccess('React application built successfully');
  } catch (error) {
    logError(`Failed to build React application: ${error.message}`);
    throw error;
  }
}

async function copyBuildFiles() {
  logStep('COPY', 'Copying build files to target directory...');
  try {
    if (!await fs.pathExists(config.sourceDir)) {
      throw new Error(`Build directory ${config.sourceDir} does not exist. Run build first.`);
    }

    // Copy all files from build directory to html directory
    await fs.copy(config.sourceDir, config.targetDir);
    logSuccess(`Copied all files from ${config.sourceDir} to ${config.targetDir}`);
  } catch (error) {
    logError(`Failed to copy build files: ${error.message}`);
    throw error;
  }
}

async function copyAdditionalAssets() {
  logStep('ASSETS', 'Copying additional assets...');
  try {
    // Copy any additional assets that might not be in the build folder
    const additionalAssets = [
      'robots.txt',
      'manifest.json',
      'favicon.ico'
    ];

    for (const asset of additionalAssets) {
      const sourcePath = path.join(config.publicDir, asset);
      const targetPath = path.join(config.targetDir, asset);
      
      if (await fs.pathExists(sourcePath)) {
        await fs.copy(sourcePath, targetPath);
        logSuccess(`Copied ${asset}`);
      }
    }
  } catch (error) {
    logWarning(`Some additional assets could not be copied: ${error.message}`);
  }
}

async function validateBuild() {
  logStep('VALIDATE', 'Validating build output...');
  try {
    const requiredFiles = ['index.html'];
    const requiredDirs = ['static'];

    for (const file of requiredFiles) {
      const filePath = path.join(config.targetDir, file);
      if (!await fs.pathExists(filePath)) {
        throw new Error(`Required file ${file} not found in build output`);
      }
      logSuccess(`Found required file: ${file}`);
    }

    for (const dir of requiredDirs) {
      const dirPath = path.join(config.targetDir, dir);
      if (!await fs.pathExists(dirPath)) {
        throw new Error(`Required directory ${dir} not found in build output`);
      }
      logSuccess(`Found required directory: ${dir}`);
    }

    // Check file sizes
    const indexPath = path.join(config.targetDir, 'index.html');
    const stats = await fs.stat(indexPath);
    if (stats.size === 0) {
      throw new Error('index.html is empty');
    }
    logSuccess(`index.html size: ${(stats.size / 1024).toFixed(2)} KB`);

  } catch (error) {
    logError(`Build validation failed: ${error.message}`);
    throw error;
  }
}

async function generateBuildInfo() {
  logStep('INFO', 'Generating build information...');
  try {
    const buildInfo = {
      buildDate: new Date().toISOString(),
      buildVersion: require('../package.json').version,
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch
    };

    const buildInfoPath = path.join(config.targetDir, 'build-info.json');
    await fs.writeJson(buildInfoPath, buildInfo, { spaces: 2 });
    logSuccess('Generated build-info.json');
  } catch (error) {
    logWarning(`Could not generate build info: ${error.message}`);
  }
}

async function main() {
  const startTime = Date.now();
  
  log(`${colors.bright}${colors.magenta}🚀 Starting deployment build process...${colors.reset}\n`);

  try {
    await cleanTargetDirectory();
    await buildReactApp();
    await copyBuildFiles();
    await copyAdditionalAssets();
    await validateBuild();
    await generateBuildInfo();

    const duration = ((Date.now() - startTime) / 1000).toFixed(2);
    
    log(`\n${colors.bright}${colors.green}🎉 Build completed successfully!${colors.reset}`);
    log(`${colors.green}📁 Deployable files are ready in: ${config.targetDir}/${colors.reset}`);
    log(`${colors.green}⏱️  Build time: ${duration}s${colors.reset}\n`);
    
    log(`${colors.cyan}Next steps:${colors.reset}`);
    log(`  1. Test the build: ${colors.yellow}cd ${config.targetDir} && python -m http.server 8000${colors.reset}`);
    log(`  2. Deploy the ${colors.yellow}${config.targetDir}/${colors.reset} folder to your web server`);
    
  } catch (error) {
    log(`\n${colors.bright}${colors.red}💥 Build failed!${colors.reset}`);
    logError(error.message);
    process.exit(1);
  }
}

// Run the build process
if (require.main === module) {
  main();
}

module.exports = { main, config };
