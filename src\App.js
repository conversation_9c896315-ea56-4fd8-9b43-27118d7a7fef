import React from "react";
import { FaLaptopCode, FaServer, FaShieldAlt, FaEnvelope, FaPhone, FaCertificate, FaNetworkWired, FaCode, FaLock } from "react-icons/fa";
import { motion } from "framer-motion";

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-gray-900 to-slate-800 text-white flex flex-col items-center justify-center space-y-8 sm:space-y-12 lg:space-y-20 p-4 sm:p-6 lg:p-12 font-sans">

      {/* Jméno a hlavní nadpis */}
      <motion.div
        className="text-center space-y-3 sm:space-y-4 lg:space-y-6 flex flex-col justify-center items-center min-h-[40vh] sm:min-h-[45vh] lg:min-h-[50vh] px-2"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-black text-white text-shadow leading-tight tracking-tight">
          Martin Vašíček
        </h1>
        <h2 className="text-lg sm:text-xl md:text-2xl lg:text-3xl xl:text-5xl font-bold gradient-text leading-snug px-2 tracking-wide">
          Vývoj & Bezpečnost | IT & Servery
        </h2>
        <div className="w-24 sm:w-32 lg:w-40 h-1 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mt-4 sm:mt-6"></div>
      </motion.div>

      {/* Popis */}
      <motion.div
        className="text-center max-w-xs sm:max-w-lg md:max-w-2xl lg:max-w-4xl px-4"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.8 }}
      >
        <p className="text-sm sm:text-base md:text-lg lg:text-xl xl:text-2xl text-gray-300 leading-relaxed tracking-wide cursor-default mb-4">
          Vyvíjím webové a desktopové aplikace na zakázku, zajišťuji IT bezpečnost včetně testování a zabezpečování systémů
          a spravuji VPS servery na Linuxu.
        </p>
        <p className="text-xs sm:text-sm md:text-base lg:text-lg text-gray-400 leading-relaxed">
          Mám hluboké znalosti v oblasti IT a pomáhám firmám i jednotlivcům s technologiemi budoucnosti.
        </p>
      </motion.div>

      {/* Dovednosti */}
      <div className="max-w-6xl w-full bg-gradient-to-br from-slate-800 to-gray-800 p-4 sm:p-6 lg:p-10 rounded-2xl lg:rounded-3xl shadow-2xl text-center border border-slate-600 hover:border-blue-500 transition-colors duration-300 mx-4 backdrop-blur-sm">
        <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-white mb-2 cursor-default">Moje dovednosti</h2>
        <div className="w-16 sm:w-20 lg:w-24 h-0.5 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mx-auto mb-6 sm:mb-8"></div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 sm:gap-6 lg:gap-8">
          <BigSkillCard icon={<FaCode size={60} />} iconSizeLg={80} title="Vývoj aplikací" description="Tvořím webové i desktopové aplikace na zakázku s důrazem na výkon a bezpečnost." />
          <BigSkillCard icon={<FaLock size={60} />} iconSizeLg={80} title="IT bezpečnost" description="Provádím testování a zabezpečování systémů proti hrozbám." />
          <BigSkillCard icon={<FaServer size={60} />} iconSizeLg={80} title="Správa VPS" description="Spravuji VPS servery na Linuxu s optimalizací pro bezpečnost a výkon." />
        </div>
      </div>

      {/* Certifikace */}
      <div className="max-w-6xl w-full bg-gradient-to-br from-slate-800 to-gray-800 p-4 sm:p-6 lg:p-12 rounded-2xl lg:rounded-3xl shadow-2xl text-center border border-slate-600 hover:border-blue-500 transition-colors duration-300 mx-4 backdrop-blur-sm">
        <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-white mb-2 cursor-default">Certifikace</h2>
        <div className="w-16 sm:w-20 lg:w-24 h-0.5 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mx-auto mb-6 sm:mb-8"></div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6 lg:gap-8">
          <CertCard icon={<FaCertificate size={40} />} iconSizeLg={50} title="CCST" description="Cisco Certified Support Technician" />
          <CertCard icon={<FaNetworkWired size={40} />} iconSizeLg={50} title="CCNA" description="Cisco Certified Network Associate" />
        </div>
      </div>

      {/* Reference */}
      <div className="max-w-6xl w-full bg-gradient-to-br from-slate-800 to-gray-800 p-4 sm:p-6 lg:p-10 rounded-2xl lg:rounded-3xl shadow-2xl text-center border border-slate-600 hover:border-blue-500 transition-colors duration-300 mx-4 backdrop-blur-sm">
        <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-white mb-2 cursor-default">Reference</h2>
        <div className="w-16 sm:w-20 lg:w-24 h-0.5 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mx-auto mb-6 sm:mb-8"></div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
          <ReferenceCard logo="/logo1.png" title="Vytvoř si potisk.cz" description="Dlouhodobá správa webového serveru (VPS), vývoj webových aplikací, opravy chyb." link="https://vytvorsipotisk.cz" />
          <ReferenceCard logo="/logo2.png" title="VR Centrum ČB" description="Vývoj specializovaných PC aplikací pro usnadnění provozu VR herny." link="https://vrcentrum.cz" />
          <ReferenceCard logo="/logo3.png" title="TransCity.cz" description="Návrh a vývoj technické části webu." link="https://transcity.cz" />
          <ReferenceCard logo="/logo4.png" title="Chat na web.cz" description="Testování zabezpečení webu, konzultace ohledně bezpečnostních opatření." link="https://chatnaweb.cz" />
        </div>
      </div>

      {/* Kontakt */}
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.6 }}
        className="w-full max-w-3xl bg-gradient-to-br from-slate-800 to-gray-800 p-6 sm:p-8 lg:p-12 rounded-2xl lg:rounded-3xl shadow-2xl text-center border border-slate-600 hover:border-blue-500 transition-all duration-300 mx-4 backdrop-blur-sm hover:shadow-blue-500/20"
      >
        <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-white mb-2 cursor-default">Kontakt</h2>
        <div className="w-16 sm:w-20 lg:w-24 h-0.5 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mx-auto mb-6"></div>
        <p className="text-base sm:text-lg lg:text-xl text-gray-300 mb-6 sm:mb-8 cursor-default px-2 leading-relaxed">
          Hledáte spolehlivého specialistu na IT? Ozvěte se mi.
        </p>
        <div className="flex flex-col md:flex-row justify-center items-center space-y-4 sm:space-y-6 md:space-y-0 md:space-x-10">
          <ContactInfo icon={<FaEnvelope />} text="<EMAIL>" />
        </div>
      </motion.div>
    </div>
  );
}

/* Komponenty */
function BigSkillCard({ icon, iconSizeLg, title, description }) {
  return (
    <div className="bg-gradient-to-br from-slate-900 to-gray-900 p-4 sm:p-6 lg:p-12 rounded-xl shadow-lg text-center flex flex-col items-center transition-all duration-300 transform hover:scale-105 lg:hover:scale-110 hover:shadow-xl border border-slate-700 hover:border-blue-400 cursor-default group">
      <div className="text-blue-400 mb-3 sm:mb-4 lg:hidden group-hover:text-blue-300 transition-colors duration-300">{icon}</div>
      <div className="text-blue-400 mb-4 hidden lg:block group-hover:text-blue-300 transition-colors duration-300">
        {React.cloneElement(icon, { size: iconSizeLg })}
      </div>
      <h3 className="text-lg sm:text-xl lg:text-2xl xl:text-3xl font-bold text-white mb-3 group-hover:text-blue-100 transition-colors duration-300">{title}</h3>
      <p className="text-gray-300 text-sm sm:text-base lg:text-lg leading-relaxed group-hover:text-gray-200 transition-colors duration-300">{description}</p>
    </div>
  );
}

function CertCard({ icon, iconSizeLg, title, description }) {
  return (
    <div className="flex flex-col items-center bg-gradient-to-br from-slate-900 to-gray-900 p-4 sm:p-6 lg:p-8 rounded-xl shadow-lg text-center border border-blue-500/50 hover:border-blue-400 hover:shadow-2xl hover:shadow-blue-500/20 transition-all duration-300 hover:scale-105 group">
      <div className="text-blue-400 mb-3 sm:mb-4 flex justify-center lg:hidden group-hover:text-blue-300 transition-colors duration-300">{icon}</div>
      <div className="text-blue-400 mb-4 flex justify-center hidden lg:block group-hover:text-blue-300 transition-colors duration-300">
        {React.cloneElement(icon, { size: iconSizeLg })}
      </div>
      <h3 className="text-lg sm:text-xl lg:text-2xl font-bold text-white group-hover:text-blue-100 transition-colors duration-300">{title}</h3>
      <p className="text-gray-400 mt-2 text-sm sm:text-base group-hover:text-gray-300 transition-colors duration-300">{description}</p>
    </div>
  );
}

function ReferenceCard({ logo, title, description, link }) {
  return (
    <a href={link} target="_blank" rel="noopener noreferrer" className="block bg-gradient-to-br from-slate-900 to-gray-900 p-4 sm:p-6 rounded-xl shadow-lg hover:shadow-2xl hover:shadow-blue-500/10 transition-all duration-300 hover:scale-105 border border-slate-700 hover:border-blue-400 group">
      <img src={logo} alt={title} className="h-12 sm:h-14 lg:h-16 mx-auto mb-3 sm:mb-4 object-contain group-hover:scale-110 transition-transform duration-300" />
      <h3 className="text-lg sm:text-xl lg:text-2xl font-bold text-white mb-2 group-hover:text-blue-100 transition-colors duration-300">{title}</h3>
      <p className="text-gray-400 text-sm sm:text-base leading-relaxed group-hover:text-gray-300 transition-colors duration-300">{description}</p>
    </a>
  );
}

function ContactInfo({ icon, text }) {
  return (
    <div className="flex items-center space-x-3 sm:space-x-4 text-base sm:text-lg lg:text-xl text-gray-300 cursor-default group">
      <div className="text-blue-400 text-2xl sm:text-3xl group-hover:text-blue-300 transition-colors duration-300">{icon}</div>
      <span className="font-medium break-all sm:break-normal group-hover:text-white transition-colors duration-300">{text}</span>
    </div>
  );
}
