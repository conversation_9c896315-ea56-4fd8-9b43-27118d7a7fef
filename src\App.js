import React from "react";
import { FaLaptopCode, FaServer, FaShieldAlt, FaEnvelope, FaPhone, FaCertificate, FaNetworkWired, FaCode, FaLock } from "react-icons/fa";
import { motion } from "framer-motion";

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-800 text-white flex flex-col items-center justify-center space-y-20 p-12">
      
      {/* Jméno a hlavní nadpis */}
      <motion.div
        className="text-center space-y-6 flex flex-col justify-center items-center min-h-[50vh]"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <h1 className="text-7xl font-extrabold text-white drop-shadow-lg"><PERSON></h1>
        <h2 className="text-5xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-500 leading-snug">
          Vývoj & Bezpečnost | IT & Servery
        </h2>
      </motion.div>

      {/* Popis */}
      <motion.p
        className="text-2xl text-gray-300 text-center max-w-4xl tracking-wide cursor-default"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.8 }}
      >
        Vyvíjím webové a desktopové aplikace na zakázku, zajišťuji IT bezpečnost včetně testování a zabezpečování systémů
        a spravuji VPS servery na Linuxu. Mám hluboké znalosti v oblasti IT a pomáhám firmám i jednotlivcům s technologiemi budoucnosti.
      </motion.p>

      {/* Dovednosti */}
      <div className="max-w-6xl w-full bg-gray-800 p-10 rounded-3xl shadow-2xl text-center border border-blue-500">
        <h2 className="text-4xl font-bold text-white mb-6 cursor-default">Moje dovednosti</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <BigSkillCard icon={<FaCode size={80} />} title="Vývoj aplikací" description="Tvořím webové i desktopové aplikace na zakázku s důrazem na výkon a bezpečnost." />
          <BigSkillCard icon={<FaLock size={80} />} title="IT bezpečnost" description="Provádím testování a zabezpečování systémů proti hrozbám." />
          <BigSkillCard icon={<FaServer size={80} />} title="Správa VPS" description="Spravuji VPS servery na Linuxu s optimalizací pro bezpečnost a výkon." />
        </div>
      </div>

      {/* Certifikace */}
      <div className="max-w-6xl w-full bg-gray-800 p-12 rounded-3xl shadow-2xl text-center border border-blue-500 mt-10">
        <h2 className="text-4xl font-bold text-white mb-6 cursor-default">Certifikace</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <CertCard icon={<FaCertificate size={50} />} title="CCST" description="Cisco Certified Support Technician" />
          <CertCard icon={<FaNetworkWired size={50} />} title="CCNA" description="Cisco Certified Network Associate" />
        </div>
      </div>

      {/* Reference */}
      <div className="max-w-6xl w-full bg-gray-800 p-10 rounded-3xl shadow-2xl text-center border border-blue-500 mt-10">
        <h2 className="text-4xl font-bold text-white mb-6 cursor-default">Reference</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6">
          <ReferenceCard logo="/logo1.png" title="Vytvoř si potisk.cz" description="Dlouhodobá správa webového serveru (VPS), vývoj webových aplikací, opravy chyb." link="https://vytvorsipotisk.cz" />
          <ReferenceCard logo="/logo2.png" title="VR Centrum ČB" description="Vývoj specializovaných PC aplikací pro usnadnění provozu VR herny." link="https://vrcentrum.cz" />
          <ReferenceCard logo="/logo3.png" title="TransCity.cz" description="Návrh a vývoj technické části webu." link="https://transcity.cz" />
          <ReferenceCard logo="/logo4.png" title="Chat na web.cz" description="Testování zabezpečení webu, konzultace ohledně bezpečnostních opatření." link="https://chatnaweb.cz" />
        </div>
      </div>

      {/* Kontakt */}
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.6 }}
        className="w-full max-w-3xl bg-gray-800 p-12 rounded-3xl shadow-2xl text-center border border-blue-500 mt-16"
      >
        <h2 className="text-4xl font-bold text-white mb-6 cursor-default">Kontakt</h2>
        <p className="text-xl text-gray-300 mb-8 cursor-default">Hledáte spolehlivého specialistu na IT? Ozvěte se mi.</p>
        <div className="flex flex-col md:flex-row justify-center items-center space-y-6 md:space-y-0 md:space-x-10">
          <ContactInfo icon={<FaEnvelope />} text="<EMAIL>" />
        </div>
      </motion.div>
    </div>
  );
}

/* Komponenty */
function BigSkillCard({ icon, title, description }) {
  return (
    <div className="bg-gray-900 p-12 rounded-xl shadow-lg text-center flex flex-col items-center transition-all transform hover:scale-110 hover:bg-gray-700 border border-gray-700 hover:border-blue-500 cursor-default">
      <div className="text-blue-400 mb-4">{icon}</div>
      <h3 className="text-3xl font-bold text-white mb-2">{title}</h3>
      <p className="text-gray-300 text-lg">{description}</p>
    </div>
  );
}

function CertCard({ icon, title, description }) {
  return (
    <div className="flex flex-col items-center bg-gray-900 p-8 rounded-xl shadow-lg text-center border border-blue-500 hover:shadow-2xl transition-transform hover:scale-105">
      <div className="text-blue-400 text-5xl mb-4 flex justify-center">{icon}</div>
      <h3 className="text-2xl font-bold text-white">{title}</h3>
      <p className="text-gray-400 mt-2">{description}</p>
    </div>
  );
}

function ReferenceCard({ logo, title, description, link }) {
  return (
    <a href={link} target="_blank" rel="noopener noreferrer" className="block bg-gray-900 p-6 rounded-xl shadow-lg hover:shadow-2xl transition-transform hover:scale-105">
      <img src={logo} alt={title} className="h-16 mx-auto mb-4" />
      <h3 className="text-2xl font-bold text-white">{title}</h3>
      <p className="text-gray-400 mt-2">{description}</p>
    </a>
  );
}

function ContactInfo({ icon, text }) {
  return (
    <div className="flex items-center space-x-4 text-xl text-gray-300 cursor-default">
      <div className="text-blue-400 text-3xl">{icon}</div>
      <span className="font-medium">{text}</span>
    </div>
  );
}
